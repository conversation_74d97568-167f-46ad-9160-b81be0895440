{"indexes": [{"v": {"$numberInt": "2"}, "key": {"_id": {"$numberInt": "1"}}, "name": "_id_"}, {"v": {"$numberInt": "2"}, "key": {"endDate": {"$numberInt": "-1"}, "startDate": {"$numberInt": "-1"}}, "name": "date", "background": true}, {"v": {"$numberInt": "2"}, "key": {"reference_number": {"$numberInt": "1"}, "status": {"$numberInt": "1"}}, "name": "reference_number_1_status_1", "background": true}, {"v": {"$numberInt": "2"}, "key": {"time": {"$numberInt": "-1"}}, "name": "timeIndex", "background": false}, {"v": {"$numberInt": "2"}, "key": {"userId": {"$numberInt": "1"}, "reference_number": {"$numberInt": "1"}, "approved": {"$numberInt": "1"}, "isDeleted": {"$numberInt": "1"}, "edited": {"$numberInt": "1"}, "radiusRateId": {"$numberInt": "1"}}, "name": "userId_1_reference_number_1_approved_1_isDeleted_1_edited_1_radiusRateId_1"}, {"v": {"$numberInt": "2"}, "key": {"userId": {"$numberInt": "1"}, "reference_number": {"$numberInt": "1"}, "approved": {"$numberInt": "1"}, "isDeleted": {"$numberInt": "1"}, "driverPayRateId": {"$numberInt": "1"}, "edited": {"$numberInt": "1"}}, "name": "userId_1_reference_number_1_approved_1_isDeleted_1_driverPayRateId_1_edited_1"}, {"v": {"$numberInt": "2"}, "key": {"terminal": {"$numberInt": "1"}, "userId": {"$numberInt": "1"}, "reference_number": {"$numberInt": "1"}, "isDeleted": {"$numberInt": "1"}}, "name": "terminal_1_userId_1_reference_number_1_isDeleted_1"}, {"v": {"$numberInt": "2"}, "key": {"createdBy": {"$numberInt": "1"}, "dynamicDriverPayId": {"$numberInt": "1"}, "toEventId": {"$numberInt": "1"}, "reference_number": {"$numberInt": "1"}, "status": {"$numberInt": "1"}}, "name": "createdBy_1_dynamicDriverPayId_1_toEventId_1_reference_number_1_status_1"}, {"v": {"$numberInt": "2"}, "key": {"createdBy": {"$numberInt": "1"}, "dynamicDriverPayId": {"$numberInt": "1"}, "reference_number": {"$numberInt": "1"}, "status": {"$numberInt": "1"}}, "name": "createdBy_1_dynamicDriverPayId_1_reference_number_1_status_1"}], "uuid": "0bdc6e4614fd4655919b9fdf04d4b8ba", "collectionName": "driverpays", "type": "collection"}