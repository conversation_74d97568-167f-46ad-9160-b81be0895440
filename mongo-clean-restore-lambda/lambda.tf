
terraform {
  required_version = ">= 1.3 "

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = ">=5.28.0"
    }

  }

}

provider "aws" {
  region = "us-east-1"
  assume_role {
    role_arn     = "arn:aws:iam::338521073506:role/deployment"
    session_name = "deployment"
  }
}

resource "aws_iam_role" "lambda_role" {
  name = "fireflink_lambda_role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Effect = "Allow"
      Principal = {
        Service = "lambda.amazonaws.com"
      }
      Action = "sts:AssumeRole"
    }]
  })

}

resource "aws_iam_role_policy_attachment" "lambda_attach_policy" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole"
}

resource "aws_iam_policy" "s3_access_policy" {
  name = "s3_access_policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [{
      Effect = "Allow"
      Action = [
        "s3:GetObject",
        "s3:GetObjectVersion",
        "s3:ListBucket"
      ]
      Resource = [
        "arn:aws:s3:::fireflink-db-dump",
        "arn:aws:s3:::fireflink-db-dump/*"
      ]
      },
      {
        Effect = "Allow",
        Action = [
          "ec2:CreateNetworkInterface",
          "ec2:DescribeNetworkInterfaces",
          "ec2:DeleteNetworkInterface"
        ],
        Resource = "*"
      }
      , {
        Effect = "Allow",
        Action = [
          "lambda:InvokeFunction"
        ],
        Resource = aws_lambda_function.lambda_function.arn
    }]
  })
}

resource "aws_iam_role_policy_attachment" "s3_access_attach" {
  role       = aws_iam_role.lambda_role.name
  policy_arn = aws_iam_policy.s3_access_policy.arn
}

resource "aws_lambda_function" "lambda_function" {
  function_name    = "fireflink_db_restore_function"
  handler          = "index.handler"
  runtime          = "nodejs16.x"
  filename         = "fireflink-db-restore-script.zip"
  source_code_hash = data.archive_file.lambda_zip.output_base64sha256
  role             = aws_iam_role.lambda_role.arn
  timeout          = 300
  memory_size      = 512

  environment {
    variables = {
      # AWS_REGION       = "us-east-1",  # Update with your desired AWS region
      MONGO_URI = "fireflink:<EMAIL>",
      DB_NAME   = "fireflink-live",
    }
  }

  vpc_config {
    ipv6_allowed_for_dual_stack = false
    security_group_ids = [
      "sg-01f76f497e2eafdf0",
      "sg-0581191b0ede18894",
    ]
    subnet_ids = [
      "subnet-017802440ce496a31",
      "subnet-0aa99ff9b63545d8f",
      "subnet-0dfd410fa9f57be27",
    ]
  }
}

data "archive_file" "lambda_zip" {
  type        = "zip"
  source_dir  = "./files/fireflink-db-restore-script/" # Directory containing your Node.js code
  output_path = "fireflink-db-restore-script.zip"
}

resource "aws_lambda_function_url" "latest_url" {
  function_name      = aws_lambda_function.lambda_function.function_name
  authorization_type = "NONE"
}

output "function_url" {
  value = aws_lambda_function_url.latest_url.function_url
}


# API Gateway v2 (HTTP API)
resource "aws_apigatewayv2_api" "fireflink_api" {
  name          = "fireflink_api"
  protocol_type = "HTTP"
  description   = "API Gateway for Fireflink DB Restore Lambda"

  cors_configuration {
    allow_credentials = false
    allow_headers     = ["content-type", "x-amz-date", "authorization", "x-api-key", "x-amz-security-token", "x-amz-user-agent"]
    allow_methods     = ["*"]
    allow_origins     = ["*"]
    expose_headers    = ["date", "keep-alive"]
    max_age          = 86400
  }
}

resource "aws_apigatewayv2_integration" "fireflink_integration" {
  api_id             = aws_apigatewayv2_api.fireflink_api.id
  integration_type   = "AWS_PROXY"
  integration_uri    = aws_lambda_function.lambda_function.invoke_arn
  integration_method = "POST"
  payload_format_version = "2.0"
}

resource "aws_apigatewayv2_route" "fireflink_route" {
  api_id    = aws_apigatewayv2_api.fireflink_api.id
  route_key = "POST /restore"
  target    = "integrations/${aws_apigatewayv2_integration.fireflink_integration.id}"
}

# Default route for any other requests
resource "aws_apigatewayv2_route" "fireflink_default_route" {
  api_id    = aws_apigatewayv2_api.fireflink_api.id
  route_key = "$default"
  target    = "integrations/${aws_apigatewayv2_integration.fireflink_integration.id}"
}

resource "aws_apigatewayv2_stage" "fireflink_stage" {
  api_id      = aws_apigatewayv2_api.fireflink_api.id
  name        = "$default"
  auto_deploy = true

  access_log_settings {
    destination_arn = aws_cloudwatch_log_group.api_gateway_logs.arn
    format = jsonencode({
      requestId      = "$context.requestId"
      ip             = "$context.identity.sourceIp"
      requestTime    = "$context.requestTime"
      httpMethod     = "$context.httpMethod"
      routeKey       = "$context.routeKey"
      status         = "$context.status"
      protocol       = "$context.protocol"
      responseLength = "$context.responseLength"
    })
  }
}

# CloudWatch Log Group for API Gateway logs
resource "aws_cloudwatch_log_group" "api_gateway_logs" {
  name              = "/aws/apigateway/fireflink_api"
  retention_in_days = 14
}

# Lambda permission for API Gateway to invoke the function
resource "aws_lambda_permission" "api_gateway_invoke" {
  statement_id  = "AllowExecutionFromAPIGateway"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.lambda_function.function_name
  principal     = "apigateway.amazonaws.com"
  source_arn    = "${aws_apigatewayv2_api.fireflink_api.execution_arn}/*/*"
}

# Optional: Custom domain configuration (commented out by default)
# resource "aws_apigatewayv2_domain_name" "fireflink_domain" {
#   domain_name = "fire-db.dev.portpro.io"

#   domain_name_configuration {
#     certificate_arn = "arn:aws:acm:us-east-1:338521073506:certificate/49699ff0-aed5-4d20-8158-027938eb3873" # ACM certificate ARN
#     endpoint_type   = "REGIONAL"
#     security_policy = "TLS_1_2"
#   }
# }

# resource "aws_apigatewayv2_api_mapping" "fireflink_mapping" {
#   domain_name = aws_apigatewayv2_domain_name.fireflink_domain.domain_name
#   api_id      = aws_apigatewayv2_api.fireflink_api.id
#   stage       = aws_apigatewayv2_stage.fireflink_stage.name
# }

# Output the API Gateway URL
output "api_gateway_url" {
  value = aws_apigatewayv2_api.fireflink_api.api_endpoint
  description = "API Gateway endpoint URL"
}

output "api_gateway_invoke_url" {
  value = "${aws_apigatewayv2_api.fireflink_api.api_endpoint}/restore"
  description = "API Gateway invoke URL for the restore endpoint"
}
